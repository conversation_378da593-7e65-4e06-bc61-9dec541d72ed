#!/usr/bin/env python3
"""
Ultra simple test server for Smithery debugging.
"""
import os
from fastapi import FastAPI
import uvicorn

print("🚀 Starting simple test server...")

app = FastAPI()

@app.get("/")
def root():
    return {"message": "Hello from MCP Server!", "status": "working"}

@app.get("/health")
def health():
    return {"status": "healthy"}

@app.get("/env")
def env_check():
    return {
        "port": os.environ.get("PORT", "not set"),
        "transport": os.environ.get("MCP_TRANSPORT", "not set"),
        "api_key": "set" if os.environ.get("OPENAI_API_KEY") else "not set"
    }

if __name__ == "__main__":
    port = int(os.environ.get("PORT", 8000))
    print(f"🌐 Starting on port {port}")
    uvicorn.run(app, host="0.0.0.0", port=port)
