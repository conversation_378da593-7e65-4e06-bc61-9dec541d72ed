name: prompt-response-server
description: A Model Context Protocol server for prompt-response interactions
version: 1.0.0
type: stdio
entry: python main.py

tools:
  - name: send_prompt
    description: Send a prompt and get a response from the AI system
  - name: get_conversation_history
    description: Get the conversation history
  - name: clear_conversation_history
    description: Clear the conversation history
  - name: get_server_status
    description: Get the current status of the MCP server

categories:
  - ai
  - conversation
  - prompt

tags:
  - prompt
  - response
  - conversation
  - ai
  - mcp

author:
  name: Your Name
  email: <EMAIL>

repository:
  type: git
  url: https://github.com/yourusername/prompt-response-mcp

license: MIT
