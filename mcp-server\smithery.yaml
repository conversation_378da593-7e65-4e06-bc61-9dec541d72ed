name: prompt-response-server
description: A Model Context Protocol server for prompt-response interactions with OpenAI integration
version: 1.0.2
type: http
entry: python start.py

# Runtime configuration
runtime:
  timeout: 300
  memory: 512

# Health check configuration
health_check:
  path: "/health"
  interval: 30
  timeout: 10
  retries: 3

# Environment variables required for deployment
environment:
  - name: OPENAI_API_KEY
    description: OpenAI API key for AI responses
    required: true
    secret: true
  - name: MCP_TRANSPORT
    description: Transport type (http for web deployment)
    default: "http"
  - name: PORT
    description: Server port
    default: "8000"
  - name: HOST
    description: Server host
    default: "0.0.0.0"

tools:
  - name: send_prompt
    description: Send a prompt and get a response from the AI system
  - name: get_conversation_history
    description: Get the conversation history
  - name: clear_conversation_history
    description: Clear the conversation history
  - name: get_server_status
    description: Get the current status of the MCP server

categories:
  - ai
  - conversation
  - prompt

tags:
  - prompt
  - response
  - conversation
  - ai
  - mcp

author:
  name: Samet Bassoy
  email: <EMAIL>

repository:
  type: git
  url: https://github.com/sametbassoy/mcp-server

license: MIT
