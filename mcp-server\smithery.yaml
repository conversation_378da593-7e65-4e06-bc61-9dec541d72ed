name: prompt-response-server
description: A Model Context Protocol server for prompt-response interactions with OpenAI integration
version: 1.0.0
type: http
entry: python main.py

# Environment variables required for deployment
environment:
  - name: OPENAI_API_KEY
    description: OpenAI API key for AI responses
    required: true
    secret: true
  - name: MCP_TRANSPORT
    description: Transport type (http for web deployment)
    default: "http"
  - name: PORT
    description: Server port
    default: "8000"

tools:
  - name: send_prompt
    description: Send a prompt and get a response from the AI system
  - name: get_conversation_history
    description: Get the conversation history
  - name: clear_conversation_history
    description: Clear the conversation history
  - name: get_server_status
    description: Get the current status of the MCP server

categories:
  - ai
  - conversation
  - prompt

tags:
  - prompt
  - response
  - conversation
  - ai
  - mcp

author:
  name: <PERSON><PERSON>
  email: <EMAIL>

repository:
  type: git
  url: https://github.com/sametbassoy/mcp-server

license: MIT
