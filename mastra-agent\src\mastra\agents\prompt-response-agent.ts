import { Agent } from '@mastra/core';
import { z } from 'zod';

// Define the schema for our MCP server communication
const PromptResponseSchema = z.object({
  prompt: z.string().describe('The prompt to send to the MCP server'),
});

const ResponseSchema = z.object({
  response: z.string().describe('The response from the MCP server'),
  status: z.string().describe('Status of the operation'),
});

// Create the Prompt Response Agent
export const promptResponseAgent = new Agent({
  name: 'PromptResponseAgent',
  instructions: `
    You are a Prompt Response Agent that communicates with an MCP (Model Context Protocol) server.
    
    Your capabilities:
    1. Send prompts to the MCP server and receive responses
    2. Maintain conversation history through the MCP server
    3. Provide status information about the MCP server
    4. Clear conversation history when requested
    
    You should:
    - Be helpful and responsive to user queries
    - Use the MCP server tools to process prompts and maintain conversation state
    - Provide clear and informative responses
    - Handle errors gracefully
  `,
  model: {
    provider: 'openai',
    name: 'gpt-4o-mini',
  },
  tools: {
    sendPromptToMCP: {
      description: 'Send a prompt to the MCP server and get a response',
      parameters: PromptResponseSchema,
      execute: async ({ prompt }) => {
        try {
          // Connect to our MCP server
          const mcpServerUrl = process.env.MCP_SERVER_URL || 'http://localhost:8000';

          // Call the MCP server's send_prompt tool
          const response = await fetch(`${mcpServerUrl}/mcp/tools/send_prompt`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              prompt: prompt
            }),
          });

          if (!response.ok) {
            throw new Error(`MCP Server error: ${response.status} - ${response.statusText}`);
          }

          const data = await response.json();

          return {
            response: data.result || data.response || 'No response from MCP server',
            status: 'success',
            timestamp: new Date().toISOString(),
          };
        } catch (error) {
          return {
            response: `Error communicating with MCP server: ${error}`,
            status: 'error',
            timestamp: new Date().toISOString(),
          };
        }
      },
    },
    
    getConversationHistory: {
      description: 'Get the conversation history from the MCP server',
      parameters: z.object({}),
      execute: async () => {
        try {
          // Simulate getting conversation history from MCP server
          const history = `Conversation History:
          
[PROMPT] (Time: 1234567890.12) Hello, how are you?
[RESPONSE] (Time: 1234567890.15) MCP Server Response to: "Hello, how are you?" - This is a demo response.

[PROMPT] (Time: 1234567891.20) What can you do?
[RESPONSE] (Time: 1234567891.25) MCP Server Response to: "What can you do?" - I can process prompts and provide responses.`;
          
          return {
            history,
            status: 'success',
            timestamp: new Date().toISOString(),
          };
        } catch (error) {
          return {
            history: `Error retrieving conversation history: ${error}`,
            status: 'error',
            timestamp: new Date().toISOString(),
          };
        }
      },
    },
    
    clearConversationHistory: {
      description: 'Clear the conversation history on the MCP server',
      parameters: z.object({}),
      execute: async () => {
        try {
          // Simulate clearing conversation history on MCP server
          return {
            message: 'Conversation history has been cleared successfully.',
            status: 'success',
            timestamp: new Date().toISOString(),
          };
        } catch (error) {
          return {
            message: `Error clearing conversation history: ${error}`,
            status: 'error',
            timestamp: new Date().toISOString(),
          };
        }
      },
    },
    
    getMCPServerStatus: {
      description: 'Get the current status of the MCP server',
      parameters: z.object({}),
      execute: async () => {
        try {
          // Simulate getting MCP server status
          const status = {
            server_name: 'prompt-response-server',
            status: 'running',
            conversation_entries: 4,
            available_tools: ['send_prompt', 'get_conversation_history', 'clear_conversation_history', 'get_server_status'],
            uptime: '2 hours 15 minutes',
            last_activity: new Date().toISOString(),
          };
          
          return {
            ...status,
            timestamp: new Date().toISOString(),
          };
        } catch (error) {
          return {
            error: `Error getting MCP server status: ${error}`,
            status: 'error',
            timestamp: new Date().toISOString(),
          };
        }
      },
    },
  },
});
