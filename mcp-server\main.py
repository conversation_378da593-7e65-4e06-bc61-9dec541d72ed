from typing import Any, Dict, List
import asyncio
import json
import os
import uvicorn
import httpx
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from mcp.server.fastmcp import FastMCP

# Initialize FastMCP server
mcp = FastMCP("prompt-response-server")

# In-memory storage for conversation history
conversation_history: List[Dict[str, Any]] = []

# OpenAI API configuration
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"

async def call_openai_api(prompt: str) -> str:
    """Call OpenAI API to get a response for the given prompt."""
    if not OPENAI_API_KEY:
        return "Error: OpenAI API key not configured. Please set OPENAI_API_KEY environment variable."

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                OPENAI_API_URL,
                headers={
                    "Authorization": f"Bearer {OPENAI_API_KEY}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "gpt-4o-mini",
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are a helpful AI assistant. Provide clear, concise, and helpful responses."
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "max_tokens": 1000,
                    "temperature": 0.7
                },
                timeout=30.0
            )

            if response.status_code == 200:
                data = response.json()
                return data["choices"][0]["message"]["content"]
            else:
                return f"Error calling OpenAI API: {response.status_code} - {response.text}"

    except httpx.TimeoutException:
        return "Error: Request to OpenAI API timed out."
    except Exception as e:
        return f"Error calling OpenAI API: {str(e)}"

@mcp.tool()
async def send_prompt(prompt: str) -> str:
    """Send a prompt and get a response from the AI system.

    Args:
        prompt: The prompt/question to send to the AI system
    """
    try:
        # Store the prompt in conversation history
        conversation_entry = {
            "type": "prompt",
            "content": prompt,
            "timestamp": asyncio.get_event_loop().time()
        }
        conversation_history.append(conversation_entry)

        # Call OpenAI API to get a real AI response
        response = await call_openai_api(prompt)

        # Store the response in conversation history
        response_entry = {
            "type": "response",
            "content": response,
            "timestamp": asyncio.get_event_loop().time()
        }
        conversation_history.append(response_entry)

        return response

    except Exception as e:
        return f"Error processing prompt: {str(e)}"

@mcp.tool()
async def get_conversation_history() -> str:
    """Get the conversation history.

    Returns:
        A formatted string containing the conversation history
    """
    if not conversation_history:
        return "No conversation history available."

    formatted_history = []
    for entry in conversation_history:
        entry_type = entry["type"].upper()
        content = entry["content"]
        timestamp = entry["timestamp"]
        formatted_history.append(f"[{entry_type}] (Time: {timestamp:.2f}) {content}")

    return "\n\n".join(formatted_history)

@mcp.tool()
async def clear_conversation_history() -> str:
    """Clear the conversation history.

    Returns:
        Confirmation message
    """
    global conversation_history
    conversation_history.clear()
    return "Conversation history has been cleared."

@mcp.tool()
async def get_server_status() -> str:
    """Get the current status of the MCP server.

    Returns:
        Server status information
    """
    status = {
        "server_name": "prompt-response-server",
        "status": "running",
        "conversation_entries": len(conversation_history),
        "available_tools": ["send_prompt", "get_conversation_history", "clear_conversation_history", "get_server_status"]
    }

    return json.dumps(status, indent=2)

def run_http_server(host: str = "0.0.0.0", port: int = 8000):
    """Run the MCP server with HTTP transport."""
    print(f"Starting Prompt-Response MCP Server with HTTP transport on {host}:{port}...")

    # Create a simple FastAPI app for HTTP transport
    app = FastAPI(title="Prompt-Response MCP Server")

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # In production, specify your domains
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "server": "prompt-response-mcp"}

    @app.get("/mcp")
    async def mcp_endpoint():
        return {"message": "MCP Server is running", "tools": ["send_prompt", "get_conversation_history", "clear_conversation_history", "get_server_status"]}

    @app.post("/mcp/tools/send_prompt")
    async def http_send_prompt(request_data: dict):
        prompt = request_data.get("prompt", "")
        if not prompt:
            return {"error": "No prompt provided"}

        result = await send_prompt(prompt)
        return {"result": result}

    @app.get("/mcp/tools/get_conversation_history")
    async def http_get_conversation_history():
        result = await get_conversation_history()
        return {"result": result}

    @app.post("/mcp/tools/clear_conversation_history")
    async def http_clear_conversation_history():
        result = await clear_conversation_history()
        return {"result": result}

    @app.get("/mcp/tools/get_server_status")
    async def http_get_server_status():
        result = await get_server_status()
        return {"result": result}

    # Run the server
    uvicorn.run(app, host=host, port=port)

def main():
    """Run the MCP server."""
    print("Starting MCP Server...")

    # Check environment variables
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        print("WARNING: OPENAI_API_KEY not set. Server will return errors for AI requests.")
    else:
        print("✓ OpenAI API key configured")

    # Check if we should use HTTP transport (for Smithery deployment)
    transport_type = os.environ.get("MCP_TRANSPORT", "stdio")
    print(f"Transport type: {transport_type}")

    if transport_type.lower() == "http":
        # Get port from environment or use default
        port = int(os.environ.get("PORT", 8000))
        host = os.environ.get("HOST", "0.0.0.0")
        print(f"Starting HTTP server on {host}:{port}")
        run_http_server(host=host, port=port)
    else:
        # Use stdio transport (default)
        print("Starting Prompt-Response MCP Server with stdio transport...")
        mcp.run(transport='stdio')

if __name__ == "__main__":
    main()
