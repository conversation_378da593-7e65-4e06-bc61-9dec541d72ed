import json
import os
from http.server import <PERSON><PERSON>PServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import time

class <PERSON>P<PERSON>andler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        if self.path == '/health':
            response = {"status": "healthy", "server": "prompt-response-mcp"}
        elif self.path == '/mcp':
            response = {"message": "MCP Server is running", "tools": ["send_prompt"]}
        elif self.path == '/mcp/tools/get_conversation_history':
            response = {"result": "No conversation history available."}
        elif self.path == '/mcp/tools/get_server_status':
            response = {"result": '{"server_name": "prompt-response-server", "status": "running"}'}
        else:
            response = {"error": "Not found"}
            
        self.wfile.write(json.dumps(response).encode())

    def do_POST(self):
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        try:
            data = json.loads(post_data.decode('utf-8'))
        except:
            data = {}
            
        if self.path == '/mcp/tools/send_prompt':
            prompt = data.get('prompt', 'No prompt provided')
            # Simulate OpenAI response
            response = {
                "result": f"AI Response to: '{prompt}'\n\nThis is a simulated AI response. In a real implementation, this would call OpenAI API with your actual API key."
            }
        elif self.path == '/mcp/tools/clear_conversation_history':
            response = {"result": "Conversation history has been cleared."}
        else:
            response = {"error": "Not found"}
            
        self.wfile.write(json.dumps(response).encode())

    def log_message(self, format, *args):
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def run_server():
    server = HTTPServer(('localhost', 8000), MCPHandler)
    print("Starting MCP Server on http://localhost:8000")
    print("Press Ctrl+C to stop the server")
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\nShutting down server...")
        server.shutdown()

if __name__ == '__main__':
    run_server()
