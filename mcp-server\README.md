# Prompt-Response MCP Server

A Model Context Protocol (MCP) server that provides prompt-response functionality for AI agents.

## Features

- **send_prompt**: Send a prompt and get a response from the AI system
- **get_conversation_history**: Retrieve the conversation history
- **clear_conversation_history**: Clear the conversation history
- **get_server_status**: Get the current status of the MCP server

## Installation

1. Clone this repository
2. Install dependencies:
   ```bash
   uv add "mcp[cli]" httpx
   ```

## Usage

### Local Development

Run the server locally:
```bash
uv run python main.py
```

### With Claude Desktop

Add to your Claude Desktop configuration:
```json
{
  "mcpServers": {
    "prompt-response": {
      "command": "uv",
      "args": [
        "--directory",
        "/path/to/mcp-server",
        "run",
        "python",
        "main.py"
      ]
    }
  }
}
```

### Deploy to Smithery

This server is configured for deployment on [Smithery](https://smithery.ai/). The `smithery.yaml` and `Dockerfile` are included for easy deployment.

## Tools

### send_prompt
Send a prompt to the AI system and get a response.

**Parameters:**
- `prompt` (string): The prompt/question to send

**Returns:** Response from the AI system

### get_conversation_history
Get the complete conversation history.

**Returns:** Formatted conversation history

### clear_conversation_history
Clear all conversation history.

**Returns:** Confirmation message

### get_server_status
Get current server status and statistics.

**Returns:** JSON object with server information

## License

MIT