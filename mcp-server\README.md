# MCP Prompt-Response Server

A Model Context Protocol (MCP) server that provides prompt-response functionality with OpenAI integration.

## Features

- 🤖 **AI-Powered Responses**: Integrates with OpenAI GPT models for intelligent responses
- 💬 **Conversation History**: Maintains conversation context and history
- 🔄 **Multiple Transport Types**: Supports both stdio and HTTP transports
- 🚀 **Smithery Ready**: Configured for easy deployment with Smithery
- 🔒 **Secure**: Environment variable-based configuration for API keys

## Available Tools

- `send_prompt`: Send a prompt and get an AI response
- `get_conversation_history`: Retrieve conversation history
- `clear_conversation_history`: Clear the conversation history
- `get_server_status`: Get server status information

## Setup

### 1. Environment Configuration

⚠️ **IMPORTANT**: Never commit API keys to version control!

Copy the example environment file:
```bash
cp .env.example .env
```

Edit `.env` and add your OpenAI API key:
```env
OPENAI_API_KEY=sk-proj-your_actual_api_key_here
MCP_TRANSPORT=http
PORT=8000
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
# or if using uv:
uv sync
```

### 3. Run the Server

**For local development (stdio transport):**
```bash
python main.py
```

**For web deployment (HTTP transport):**
```bash
export MCP_TRANSPORT=http
python main.py
```

## API Endpoints (HTTP Mode)

- `GET /health` - Health check
- `GET /mcp` - Server information
- `POST /mcp/tools/send_prompt` - Send a prompt
- `GET /mcp/tools/get_conversation_history` - Get conversation history
- `POST /mcp/tools/clear_conversation_history` - Clear history
- `GET /mcp/tools/get_server_status` - Get server status

## Smithery Deployment

This server is configured for Smithery deployment. The `smithery.yaml` file includes:

- Environment variable configuration
- Secure API key handling
- HTTP transport setup

### Required Environment Variables for Smithery:

- `OPENAI_API_KEY`: Your OpenAI API key (marked as secret)
- `MCP_TRANSPORT`: Set to "http" for web deployment
- `PORT`: Server port (defaults to 8000)

## Security

⚠️ **Important Security Notes:**

1. **Never commit API keys** to version control
2. Always use environment variables for sensitive data
3. The `.env` file is in `.gitignore` for security
4. Use `.env.example` as a template only

## Tool Reference

### send_prompt
Send a prompt to the AI system and get a response.

**Parameters:**
- `prompt` (string): The prompt/question to send

**Returns:** Response from the AI system

### get_conversation_history
Get the complete conversation history.

**Returns:** Formatted conversation history

### clear_conversation_history
Clear all conversation history.

**Returns:** Confirmation message

### get_server_status
Get current server status and statistics.

**Returns:** JSON object with server information

## License

MIT License