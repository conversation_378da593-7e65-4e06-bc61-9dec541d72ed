# OpenAI API Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-proj-your_openai_api_key_here

# MCP Server Configuration
# Transport type: 'stdio' for local use, 'http' for web deployment
MCP_TRANSPORT=http

# Server port (used when MCP_TRANSPORT=http)
PORT=8000

# Optional: Server host (defaults to 0.0.0.0)
# HOST=0.0.0.0

# Optional: Enable debug mode
# DEBUG=false
