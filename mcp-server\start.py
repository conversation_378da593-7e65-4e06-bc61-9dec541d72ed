#!/usr/bin/env python3
"""
Startup script for MCP server with better error handling and logging.
"""
import os
import sys
import time
import traceback

def check_environment():
    """Check required environment variables."""
    print("🔍 Checking environment variables...")
    
    # Check OpenAI API key
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        print("⚠️  WARNING: OPENAI_API_KEY not set")
    else:
        print("✅ OpenAI API key configured")
    
    # Check transport type
    transport = os.environ.get("MCP_TRANSPORT", "stdio")
    print(f"🚀 Transport type: {transport}")
    
    # Check port
    port = os.environ.get("PORT", "8000")
    print(f"🌐 Port: {port}")
    
    return True

def main():
    """Main startup function."""
    print("=" * 50)
    print("🚀 Starting MCP Prompt-Response Server")
    print("=" * 50)
    
    try:
        # Check environment
        check_environment()
        
        print("\n📦 Importing main module...")
        from main import main as run_server
        
        print("🎯 Starting server...")
        run_server()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("📋 Traceback:")
        traceback.print_exc()
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ Startup error: {e}")
        print("📋 Traceback:")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
